import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { formDataJsonTransformation } from "../../utils/form-data-json-transformation";
import { updateProductLanguageContent } from "../../services/products/product-language-update";
import { cleanProductEditionFormData } from "../../utils/form-data-cleaning/product-edition-form";
import { validateProductEditionData } from "../../validation/products/validate-product-edition-data";

export default function useMultilanguageProductEdition(
  getMetaContent: () => MultilanguageSeoContentPayload,
  getProductId: () => string,
  onSuccessfulUpdate: () => void,
  onSlugChange?: (newSlug: string) => void
) {
  const t = useTranslations("warnings");
  const successT = useTranslations("shared.forms.upload");

  const queryClient = useQueryClient();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");
  const [formKey, setFormKey] = useState(0);

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setFormKey((prev) => prev + 1);
    setActiveLanguage(language);
  };

  async function submitProduct(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanProductEditionFormData(
        formData,
        activeLanguage
      );

      const submittedData: Record<string, any> =
        formDataJsonTransformation(filteredFormData);

      // if (submittedData.content && typeof submittedData.content === "string") {
      //   submittedData.content = JSON.parse(submittedData.content);
      // }

      validateProductEditionData(filteredFormData);

      const metaContentData = getMetaContent();
      submittedData["metaContent"] = metaContentData.content;

      setWarning("");

      const productId = getProductId();
      const response = await updateProductLanguageContent(
        submittedData,
        productId,
        activeLanguage
      );

      queryClient.invalidateQueries({
        queryKey: ["products"],
        exact: false,
      });

      if (response.data && response.data.slug && onSlugChange) {
        onSlugChange(response.data.slug);
      }

      onSuccessfulUpdate();

      toast({
        title: successT("successTitle"),
        description: successT("successDescription"),
      });
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitProduct,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
    formKey,
  };
}
