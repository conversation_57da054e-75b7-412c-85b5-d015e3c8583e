import {
  validateEditionFields,
  validateDisplayOrder,
  validateCategoryIds,
  validateJsonData,
  validateJsonCategoryIds,
  validateJsonDisplayOrder,
} from "../../../shared/validation/unified-validation";

export function validateProductEditionData(formData: FormData): void {
  validateEditionFields(formData, ["name"]);
  validateCategoryIds(formData);
  validateDisplayOrder(formData);
}

export function validateProductEditionJsonData(
  submittedData: Record<string, any>
): void {
  validateJsonData(submittedData, ["name"]);
  validateJsonCategoryIds(submittedData);
  validateJsonDisplayOrder(submittedData);
}
