import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import ImageUpload from "@/modules/catalog/components/images-management/image-upload";
import { useTranslations } from "next-intl";
import FormSubmission from "../../../form-submission";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import { CategoryType } from "@/modules/catalog/types/categories";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import useMultilanguageCategoryCreation from "@/modules/catalog/hooks/categories/use-multilanguage-category-creation";
import MultilanguageCategoryFormFields from "./multilanguage-category-form-fields";
import LanguageTabs from "../../../brands/brand-upload/language-tabs";
import { useState } from "react";
import { Language } from "@/modules/seo/types/multilanguage-seo";

interface Props {
  parentCategories: CategoryType[];
  onSuccess: () => void;
  onCancel: () => void;
}

export default function CategoryCreation({
  parentCategories,
  onSuccess,
  onCancel,
}: Props) {
  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  const [activeLanguage, setActiveLanguage] = useState("french");

  const { formRef, submitCategory, warning, isPending, handleLanguageChange } =
    useMultilanguageCategoryCreation(getMultilanguageMetaContent);

  const { categories, categoriesAreLoading } = useCategories();
  const t = useTranslations("CategoriesManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  return !(categoriesAreLoading || categories === undefined) ? (
    categories && (
      <FormSubmission
        submit={uploadContent("save")}
        cancel={uploadContent("cancel")}
        isPending={isPending}
        onCancel={onCancel}
        onSubmit={submitCategory}
        hideTopButtons
      >
        <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
          <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
            <div className="rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
              <Text textStyle="TS4" className="font-bold text-black">
                {t("subSubCategoryInfo")}
              </Text>
              <div className="text-red self-center">{warning}</div>
              <div className="flex flex-col space-y-5">
                {/* Category Type */}
                {parentCategories[parentCategories.length - 1] && (
                  <Input
                    name="parentCategoryId"
                    className="hidden"
                    readOnly
                    value={parentCategories[parentCategories.length - 1].id}
                  />
                )}

                {/* Language Tabs and Multilanguage Fields */}
                <div className="w-full">
                  <LanguageTabs
                    options={[
                      { key: "arabic", value: t("languages.arabic") },
                      { key: "french", value: t("languages.french") },
                      { key: "english", value: t("languages.english") },
                    ]}
                    onSelect={(language) => {
                      setActiveLanguage(language);
                    }}
                    selectedValue={activeLanguage}
                  />
                  <hr className="mb-4" />

                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      multilanguage={true}
                      language="arabic"
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      multilanguage={true}
                      language="french"
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageCategoryFormFields
                      multilanguage={true}
                      language="english"
                    />
                  </div>
                </div>

                {/* Display Order */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="displayOrder">
                    {`${uploadContent(
                      "categoryLabels.displayOrder"
                    )} ${uploadContent("optional")}`}
                  </Label>
                  <WarnInput
                    id="displayOrder"
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    warning=""
                    placeholder="10"
                  />
                </div>

                {/* Image */}
                <div className="w-full flex flex-col space-y-2">
                  <Label>{uploadContent("categoryLabels.image")}</Label>
                  <ImageUpload name="image" />
                </div>
              </div>
            </div>
          </div>
          <div className="basis-[30%] order-2 space-y-4">
            <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
              <MultilanguageSeoContent
                metaContent={metaContent}
                activeLanguage={activeLanguage as Language}
                changeMetaTitle={handleMetaTitleChange}
                changeMetaDescription={handleMetaDescriptionChange}
                addNewKeyword={addNewKeyword}
                removeKeyword={removeKeyword}
              />
            </div>
          </div>
        </form>
      </FormSubmission>
    )
  ) : (
    <DashboardListsContainerSkeleton className="flex-1">
      {Array.from({ length: 6 }).map((_, idx) => (
        <div key={idx} className="w-full flex flex-col space-y-1">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-9 w-full max-w-[500px]" />
        </div>
      ))}
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
    </DashboardListsContainerSkeleton>
  );
}
