import { CustomError } from "@/utils/custom-error";

export function validateMultilanguageContent(
  formData: FormData,
  includeDetails: boolean = false
): void {
  const contentField = formData.get("content");
  
  if (!contentField || typeof contentField !== "string") {
    throw new CustomError("Missed Data!", 400);
  }

  try {
    const content = JSON.parse(contentField);
    
    if (!Array.isArray(content) || content.length === 0) {
      throw new CustomError("Missed Data!", 400);
    }

    const requiredLanguages = ["Arabic", "French", "English"];
    const missingFields: string[] = [];

    requiredLanguages.forEach((lang) => {
      const langContent = content.find((item) => item.language === lang);
      
      if (!langContent) {
        missingFields.push(`${lang} content`);
        return;
      }

      if (!langContent.name || langContent.name.trim() === "") {
        missingFields.push(`${lang} name`);
      }

      if (!langContent.description || langContent.description.trim() === "") {
        missingFields.push(`${lang} description`);
      }

      if (includeDetails && (!langContent.details || langContent.details.trim() === "")) {
        missingFields.push(`${lang} details`);
      }
    });

    if (missingFields.length > 0) {
      throw new CustomError("Missed Data!", 400);
    }
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError("Invalid Data!", 400);
  }
}

export function validateEditionFields(
  formData: FormData,
  requiredFields: string[]
): void {
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    const value = formData.get(field);
    if (!value || (typeof value === "string" && value.trim() === "")) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }
}

export function validateDisplayOrder(formData: FormData): void {
  const displayOrder = formData.get("displayOrder");
  
  if (displayOrder && typeof displayOrder === "string" && displayOrder.trim() !== "") {
    const orderNumber = parseFloat(displayOrder);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}

export function validateCategoryIds(formData: FormData): void {
  const categoryIds = formData.getAll("categoryIds");
  
  if (!categoryIds || categoryIds.length === 0 || categoryIds[0] === "") {
    throw new CustomError("Missed Data!", 400);
  }
}

export function validateJsonData(
  submittedData: Record<string, any>,
  requiredFields: string[]
): void {
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    const value = submittedData[field];
    if (!value || (typeof value === "string" && value.trim() === "")) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }
}

export function validateJsonCategoryIds(submittedData: Record<string, any>): void {
  const categoryIds = submittedData.categoryIds;
  
  if (!categoryIds || 
      (Array.isArray(categoryIds) && categoryIds.length === 0) ||
      (typeof categoryIds === "string" && categoryIds === "")) {
    throw new CustomError("Missed Data!", 400);
  }
}

export function validateJsonDisplayOrder(submittedData: Record<string, any>): void {
  const displayOrder = submittedData.displayOrder;
  
  if (displayOrder && typeof displayOrder === "string" && displayOrder.trim() !== "") {
    const orderNumber = parseFloat(displayOrder);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}
