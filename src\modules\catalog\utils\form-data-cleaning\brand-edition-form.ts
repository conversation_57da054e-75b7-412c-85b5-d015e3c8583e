export function cleanBrandEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();
  const content: {
    name: string;
    description: string;
    language: string;
  }[] = [];

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  // Build content array with all languages
  ["arabic", "french", "english"].forEach((lang) => {
    const nameField = formData.get(`name_${lang}`);
    const descField = formData.get(`description_${lang}`);

    if (lang === currentLanguage) {
      // For current language, include the actual data
      const nameValue = nameField?.toString() || "";
      const descValue = descField?.toString() || "";

      content.push({
        name: nameValue,
        description: descValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    } else {
      // For other languages, include only language property
      content.push({
        name: "",
        description: "",
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  // Append non-language specific fields
  for (const [key, value] of formData.entries()) {
    if (excludedFields.includes(key)) {
      continue;
    }

    if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      cleanedData.append(key, value);
    }
  }

  // Add content array to form data
  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  return cleanedData;
}
