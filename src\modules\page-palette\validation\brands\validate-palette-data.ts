import { CustomError } from "@/utils/custom-error";

export function validatePaletteData(formData: FormData): void {
  const requiredFields: string[] = [
    "computerImage",
    "mobileImage",
    "redirectUrl",
  ];
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }

  const redirectUrl = formData.get("redirectUrl") as string;
  if (redirectUrl) {
    try {
      new URL(redirectUrl);
    } catch (error) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}
