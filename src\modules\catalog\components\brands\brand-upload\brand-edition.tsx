"use client";
import ImageUpload from "@/modules/catalog/components/images-management/image-upload";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import FormSubmission from "../../form-submission";
import { useRouter } from "next/navigation";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import useEditableBrand from "@/modules/catalog/hooks/brands/use-editable-brand";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import useMultilanguageBrandEdition from "@/modules/catalog/hooks/brands/use-multilanguage-brand-edition";
import LanguageTabs from "./language-tabs";
import MultilanguageFormFields from "./multilanguage-form-fields";
import { Language } from "@/modules/seo/types/multilanguage-seo";

interface BrandEditionProps {
  brandSlug?: string;
}

export default function BrandEdition({ brandSlug }: BrandEditionProps) {
  const t = useTranslations("BrandsManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const {
    formRef,
    submitBrand,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
    formKey,
  } = useMultilanguageBrandEdition(
    () => getMultilanguageMetaContent(),
    () => brand?.id || "",
    () => {
      refetch();
    },
    (newSlug: string) => {
      const currentPath = window.location.pathname;
      const newPath = currentPath.replace(
        /\/brands\/[^\/]+\/edit/,
        `/brands/${newSlug}/edit`
      );
      router.replace(newPath);
    }
  );

  const {
    brand,
    isLoading: brandIsLoading,
    refetch,
  } = useEditableBrand({
    brandSlug: brandSlug || "",
    language: activeLanguage,
  });

  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/brands"))
      router.push(previousUrl);
    else router.push("/brands");
  };

  return !brandIsLoading ? (
    brand && (
      <FormSubmission
        submit={uploadContent("save")}
        cancel={uploadContent("cancel")}
        isPending={isPending}
        onCancel={cancelSubmission}
        onSubmit={submitBrand}
      >
        <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
          <LanguageTabs
            options={[
              { key: "arabic", value: t("languages.arabic") },
              { key: "french", value: t("languages.french") },
              { key: "english", value: t("languages.english") },
            ]}
            onSelect={handleLanguageChange}
            selectedValue={activeLanguage}
          />
          <Text textStyle="TS4" className="font-bold text-black">
            {t("brandInfo")}
          </Text>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <Text textStyle="TS6" className="text-yellow-800">
              {uploadContent("saveBeforeSwitchingNote")}
            </Text>
          </div>

          <form
            ref={formRef}
            key={formKey}
            className="flex flex-col extraXL:flex-row gap-4"
          >
            <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
              {/* Language Content Card */}
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("brandContent")}
                </Text>
                <div className="text-red self-center">{warning}</div>

                <div className="w-full">
                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageFormFields
                      multilanguage={true}
                      language="arabic"
                      initialName={brand?.name || ""}
                      initialDescription={brand?.description || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageFormFields
                      multilanguage={true}
                      language="french"
                      initialName={brand?.name || ""}
                      initialDescription={brand?.description || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageFormFields
                      multilanguage={true}
                      language="english"
                      initialName={brand?.name || ""}
                      initialDescription={brand?.description || ""}
                    />
                  </div>
                </div>
              </div>

              {/* Images Card */}
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("brandImages")}
                </Text>
                <div className="w-full flex flex-col space-y-2">
                  <Label>{uploadContent("brandLabels.image")}</Label>
                  <ImageUpload name="image" defaultSrc={brand?.image} />
                </div>

                {/* Display Order */}
                <div className="w-full flex flex-col space-y-2">
                  <Label htmlFor="displayOrder">
                    {`${uploadContent(
                      "brandLabels.displayOrder"
                    )} ${uploadContent("optional")}`}
                  </Label>
                  <WarnInput
                    id="displayOrder"
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    value={brand?.displayOrder || ""}
                    warning=""
                    placeholder={uploadContent(
                      "brandLabels.displayOrderPlaceholder"
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="basis-[30%] order-2 space-y-4">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <MultilanguageSeoContent
                  metaContent={metaContent}
                  activeLanguage={activeLanguage as Language}
                  changeMetaTitle={handleMetaTitleChange}
                  changeMetaDescription={handleMetaDescriptionChange}
                  addNewKeyword={addNewKeyword}
                  removeKeyword={removeKeyword}
                />
              </div>
            </div>
          </form>
        </div>
      </FormSubmission>
    )
  ) : (
    <DashboardListsContainerSkeleton className="flex-1">
      {Array.from({ length: 6 }).map((_, idx) => (
        <div key={idx} className="w-full flex flex-col space-y-1">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-9 w-full max-w-[500px]" />
        </div>
      ))}
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
      <div className="w-full flex flex-col space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-48 w-full max-w-[800px]" />
      </div>
    </DashboardListsContainerSkeleton>
  );
}
