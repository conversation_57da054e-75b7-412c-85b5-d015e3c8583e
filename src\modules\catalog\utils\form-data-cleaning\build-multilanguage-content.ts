interface ContentField {
  name: string;
  description: string;
  details?: string;
  language: string;
}

export function buildMultilanguageContent(
  formData: FormData,
  currentLanguage: string,
  includeDetails: boolean = false
): ContentField[] {
  const content: ContentField[] = [];

  ["arabic", "french", "english"].forEach((lang) => {
    const nameField = formData.get(`name_${lang}`);
    const descField = formData.get(`description_${lang}`);
    const detailsField = includeDetails
      ? formData.get(`details_${lang}`)
      : null;

    if (lang === currentLanguage) {
      const contentItem: ContentField = {
        name: nameField?.toString() || "",
        description: descField?.toString() || "",
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      };

      if (includeDetails) {
        contentItem.details = detailsField?.toString() || "";
      }

      content.push(contentItem);
    } else {
      const contentItem: ContentField = {
        name: "",
        description: "",
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      };

      if (includeDetails) {
        contentItem.details = "";
      }

      content.push(contentItem);
    }
  });

  return content;
}
