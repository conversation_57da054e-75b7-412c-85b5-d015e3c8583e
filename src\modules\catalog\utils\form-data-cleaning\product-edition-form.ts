export function cleanProductEditionFormData(
  formData: FormData,
  currentLanguage: string
): FormData {
  const cleanedData = new FormData();
  const categoryIds: string[] = [];
  const content: {
    name: string;
    description: string;
    details: string;
    language: string;
  }[] = [];

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  // Build content array with all languages
  ["arabic", "french", "english"].forEach((lang) => {
    const nameField = formData.get(`name_${lang}`);
    const descField = formData.get(`description_${lang}`);
    const detailsField = formData.get(`details_${lang}`);

    if (lang === currentLanguage) {
      // For current language, include the actual data
      const nameValue = nameField?.toString() || "";
      const descValue = descField?.toString() || "";
      const detailsValue = detailsField?.toString() || "";

      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    } else {
      // For other languages, include only language property
      content.push({
        name: "",
        description: "",
        details: "",
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  // Process form data
  for (const [key, value] of formData.entries()) {
    if (excludedFields.includes(key)) {
      continue;
    }

    if (key === "categoryIds") {
      categoryIds.push(value as string);
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      if (key === "brandId" && value === "") {
        cleanedData.append(key, "null");
      } else {
        cleanedData.append(key, value);
      }
    }
  }

  categoryIds.forEach((id) => cleanedData.append("categoryIds", id));

  // Add content array to form data
  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  return cleanedData;
}
