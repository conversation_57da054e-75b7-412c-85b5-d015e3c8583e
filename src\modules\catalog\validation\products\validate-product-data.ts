import { CustomError } from "@/utils/custom-error";

export function validateProductData(formData: FormData): void {
  const requiredFields: string[] = ["name", "categoryIds"];
  const missingFields: string[] = [];

  for (const [field, value] of formData.entries()) {
    if (requiredFields.includes(field)) {
      if (value === "") missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }
}
