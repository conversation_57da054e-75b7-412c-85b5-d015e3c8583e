import {
  validateMultilanguageContent,
  validateDisplayOrder,
  validateEditionFields,
} from "../../../shared/validation/unified-validation";

export function validateBlogData(formData: FormData): void {
  validateMultilanguageContent(formData, true);
  validateDisplayOrder(formData);
}

export function validateBlogLanguageUpdate(formData: FormData): void {
  validateEditionFields(formData, ["name", "description", "details"]);
}
